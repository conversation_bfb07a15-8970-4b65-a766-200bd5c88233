# 🎯 异步处理模式示例

## 📚 常见阻塞问题及解决方案

### 1. LLM调用阻塞

**问题代码：**
```python
# ❌ 阻塞的LLM调用
@router.post("/chat")
async def chat(request: ChatRequest):
    agent = ChatAgent()
    result = agent.generate_response(request.message)  # 同步调用，阻塞事件循环
    return {"response": result}
```

**优化方案：**
```python
# ✅ 异步LLM调用
from Utils.AsyncConfig import async_config

@router.post("/chat")
async def chat(request: ChatRequest):
    agent = ChatAgent()
    
    # 在线程池中执行，添加超时控制
    try:
        result = await asyncio.wait_for(
            async_config.run_in_llm_executor(
                agent.generate_response, 
                request.message
            ),
            timeout=30.0
        )
        return {"response": result}
    except asyncio.TimeoutError:
        return {"error": "处理超时，请稍后重试"}
```

### 2. 文件处理阻塞

**问题代码：**
```python
# ❌ 阻塞的文件处理
@router.post("/upload")
async def upload_file(file: UploadFile):
    content = file.file.read()  # 同步读取
    processed = process_file(content)  # 同步处理
    return {"result": processed}
```

**优化方案：**
```python
# ✅ 异步文件处理
@router.post("/upload")
async def upload_file(file: UploadFile):
    # 异步读取文件
    content = await file.read()
    
    # 在文件处理线程池中执行
    try:
        processed = await asyncio.wait_for(
            async_config.run_in_file_executor(
                process_file, 
                content
            ),
            timeout=60.0
        )
        return {"result": processed}
    except asyncio.TimeoutError:
        return {"error": "文件处理超时"}
```

### 3. 数据库查询阻塞

**问题代码：**
```python
# ❌ 阻塞的数据库查询
@router.get("/users")
async def get_users():
    users = db.query("SELECT * FROM users")  # 同步查询
    return {"users": users}
```

**优化方案：**
```python
# ✅ 异步数据库查询
@router.get("/users")
async def get_users():
    try:
        users = await asyncio.wait_for(
            async_config.run_in_file_executor(
                lambda: db.query("SELECT * FROM users")
            ),
            timeout=10.0
        )
        return {"users": users}
    except asyncio.TimeoutError:
        return {"error": "数据库查询超时"}
```

### 4. 网络请求阻塞

**问题代码：**
```python
# ❌ 阻塞的网络请求
import requests

@router.get("/external-data")
async def get_external_data():
    response = requests.get("http://api.example.com/data")  # 同步请求
    return response.json()
```

**优化方案：**
```python
# ✅ 异步网络请求
import aiohttp

@router.get("/external-data")
async def get_external_data():
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "http://api.example.com/data",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                data = await response.json()
                return data
    except asyncio.TimeoutError:
        return {"error": "外部API请求超时"}
```

## 🛠️ 通用异步包装器模式

### 创建异步包装器类

```python
class AsyncServiceWrapper:
    """通用异步服务包装器"""
    
    def __init__(self, sync_service):
        self.sync_service = sync_service
    
    async def async_method(self, method_name: str, *args, timeout: float = 30.0, **kwargs):
        """通用异步方法调用"""
        method = getattr(self.sync_service, method_name)
        
        try:
            return await asyncio.wait_for(
                async_config.run_in_llm_executor(method, *args, **kwargs),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            raise TimeoutError(f"{method_name} 执行超时")
        except Exception as e:
            raise Exception(f"{method_name} 执行失败: {str(e)}")

# 使用示例
async def use_async_wrapper():
    sync_service = SomeService()
    async_wrapper = AsyncServiceWrapper(sync_service)
    
    # 异步调用同步方法
    result = await async_wrapper.async_method("process_data", data, timeout=60.0)
    return result
```

### 装饰器模式

```python
from functools import wraps

def async_executor(timeout: float = 30.0, executor_type: str = "llm"):
    """异步执行装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 选择合适的执行器
            if executor_type == "llm":
                executor_method = async_config.run_in_llm_executor
            elif executor_type == "file":
                executor_method = async_config.run_in_file_executor
            else:
                executor_method = async_config.run_in_download_executor
            
            try:
                return await asyncio.wait_for(
                    executor_method(func, *args, **kwargs),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                raise TimeoutError(f"{func.__name__} 执行超时")
        
        return wrapper
    return decorator

# 使用示例
@async_executor(timeout=60.0, executor_type="file")
def process_large_file(file_path: str):
    # 这是一个同步的文件处理函数
    with open(file_path, 'r') as f:
        return f.read()

# 在异步接口中使用
@router.post("/process")
async def process_file_endpoint(file_path: str):
    try:
        result = await process_large_file(file_path)
        return {"result": result}
    except TimeoutError:
        return {"error": "文件处理超时"}
```

## 📋 最佳实践清单

### ✅ 应该做的

1. **识别阻塞操作**
   - 任何可能耗时超过100ms的操作
   - LLM调用、文件I/O、网络请求、数据库查询

2. **添加超时控制**
   ```python
   await asyncio.wait_for(operation(), timeout=30.0)
   ```

3. **使用合适的线程池**
   - LLM调用：`async_config.run_in_llm_executor`
   - 文件操作：`async_config.run_in_file_executor`
   - 网络下载：`async_config.run_in_download_executor`

4. **错误处理**
   ```python
   try:
       result = await async_operation()
   except asyncio.TimeoutError:
       # 处理超时
   except Exception as e:
       # 处理其他异常
   ```

5. **资源清理**
   - 在应用关闭时清理线程池
   - 关闭数据库连接
   - 清理临时文件

### ❌ 不应该做的

1. **在async函数中调用同步阻塞操作**
   ```python
   # ❌ 错误示例
   async def bad_example():
       time.sleep(5)  # 阻塞整个事件循环
   ```

2. **忽略超时控制**
   ```python
   # ❌ 没有超时控制
   result = await some_long_operation()  # 可能永远不返回
   ```

3. **创建过多线程**
   ```python
   # ❌ 每次都创建新线程池
   executor = ThreadPoolExecutor(max_workers=100)  # 资源浪费
   ```

## 🔍 调试技巧

### 1. 监控线程池状态

```python
def check_thread_pool_status():
    """检查线程池状态"""
    from Utils.AsyncConfig import async_config
    
    print(f"LLM线程池活跃线程: {async_config.llm_executor._threads}")
    print(f"文件线程池活跃线程: {async_config.file_executor._threads}")
    print(f"下载线程池活跃线程: {async_config.download_executor._threads}")
```

### 2. 性能分析

```python
import time
from functools import wraps

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            end_time = time.time()
            print(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            print(f"{func.__name__} 执行失败，耗时: {end_time - start_time:.2f}秒，错误: {str(e)}")
            raise
    return wrapper
```

通过这些模式和最佳实践，您可以轻松地将任何同步阻塞操作转换为异步非阻塞操作，大大提升系统的并发处理能力！
