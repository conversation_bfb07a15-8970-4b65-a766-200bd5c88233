import asyncio
from concurrent.futures import ThreadPoolExecutor

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Utils.logs.LoggingConfig import logger


class AsyncImproveInfoAgent:
    """异步ImproveInfoAgent包装器，避免LLM调用阻塞其他接口"""
    
    def __init__(self):
        self._improve_agent = ImproveInfoAgent()
        # 创建线程池用于同步方法
        from Configs.Config import SysConfig
        max_workers = SysConfig.get("async_config", {}).get("max_llm_workers", 4)
        self._executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="ImproveInfo-Worker")

    async def evaluate_improve_info_async(self, evaluate: str):
        """异步版本的evaluateImproveInfo"""
        # 检查原方法是否是异步的
        if hasattr(self._improve_agent, 'evaluateImproveInfo'):
            method = getattr(self._improve_agent, 'evaluateImproveInfo')
            if asyncio.iscoroutinefunction(method):
                # 原方法已经是异步的，直接调用
                return await method(evaluate)
            else:
                # 原方法是同步的，在线程池中执行
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(self._executor, method, evaluate)
        else:
            logger.error("evaluateImproveInfo方法不存在")
            return None

    async def hireImproveInfo_async(self, position: str, content: str, interview_rounds: str):
        """异步版本的hireImproveInfo"""
        if hasattr(self._improve_agent, 'hireImproveInfo'):
            method = getattr(self._improve_agent, 'hireImproveInfo')
            if asyncio.iscoroutinefunction(method):
                # 原方法已经是异步的，直接调用
                return await method(position, content, interview_rounds)
            else:
                # 原方法是同步的，在线程池中执行
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(self._executor, method, position, content, interview_rounds)
        else:
            logger.error("hireImproveInfo方法不存在")
            return None

    def cleanup(self):
        """清理资源"""
        try:
            self._executor.shutdown(wait=True)
            logger.info("AsyncImproveInfoAgent线程池已关闭")
        except Exception as e:
            logger.error(f"清理AsyncImproveInfoAgent资源时出错: {e}")


# 创建全局异步代理实例
async_improve_info_agent = AsyncImproveInfoAgent()
